// src/components/layout/app-sidebar.tsx
'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  route: string;
  subItems?: { id: string; label: string; route: string }[];
}

interface AppSidebarProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

const AppSidebar = ({ isCollapsed, onToggleCollapse }: AppSidebarProps) => {
  const [expandedItems, setExpandedItems] = useState<string[]>(['Student Management']);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const router = useRouter();
  const pathname = usePathname();

  const sidebarItems: SidebarItem[] = [
    {
      id: 'Dashboard',
      label: 'Dashboard',
      route: '/dashboard',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
        </svg>
      ),
    },
    {
      id: 'Student Management',
      label: 'Student Management',
      route: '/student-management',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      subItems: [
        { id: 'Current Students', label: 'Current Students', route: '/student-management' },
        { id: 'Enroll Student', label: 'Enroll Student', route: '/student-management' },
        { id: 'Class Assignment', label: 'Class Assignment', route: '/student-management' },
        { id: 'School Records', label: 'School Records', route: '/student-management' },
      ],
    },
    {
      id: 'Staff Management',
      label: 'Staff Management',
      route: '/staff-management',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
    },
    {
      id: 'Attendance Management',
      label: 'Attendance',
      route: '/attendance-management',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      ),
    },
    {
      id: 'Academic Management',
      label: 'Academic',
      route: '/academic-management',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
    },
    {
      id: 'Fee Management',
      label: 'Fee Management',
      route: '/fee-management',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
    },
    {
      id: 'Reports',
      label: 'Reports',
      route: '/reports', // Fixed route to prevent conflicts with dashboard
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
    },
  ];

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleItemClick = (item: SidebarItem) => {
    if (item.subItems) {
      toggleExpanded(item.id);
    } else {
      router.push(item.route);
    }
  };

  const handleSubItemClick = (subItem: { id: string; label: string; route: string }) => {
    router.push(subItem.route);
  };

  const isActiveRoute = (route: string) => {
    return pathname === route || pathname.startsWith(route + '/');
  };

  return (
    <div className={`sidebar-dark fixed left-0 top-0 h-full border-r transition-all duration-300 z-40 ${
      isCollapsed ? 'w-16' : 'w-72'
    }`}>
      {/* Modern Collapse Toggle Arrow - Positioned in middle of sidebar */}
      <div 
        className={`sidebar-collapse-toggle ${isCollapsed ? 'collapsed' : ''}`}
        onClick={onToggleCollapse}
        style={{ top: '50%' }}
      >
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
        </svg>
      </div>

      {/* Sidebar Header */}
      <div className="sidebar-header sidebar-header-height flex items-center justify-center px-4 border-b">
        {!isCollapsed && (
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-400 rounded-xl flex items-center justify-center shadow-lg shadow-blue-400/40">
              <span className="text-white font-bold text-base-app">EP</span>
            </div>
            <span className="sidebar-brand text-lg-app">EduPro</span>
          </div>
        )}
        {isCollapsed && (
          <div className="flex items-center justify-center w-full">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-400 rounded-lg flex items-center justify-center shadow-lg shadow-blue-400/40">
              <span className="text-white font-bold text-sm-app">EP</span>
            </div>
          </div>
        )}
      </div>

      {/* Navigation Items */}
      <nav className={`mt-6 ${isCollapsed ? 'px-2' : 'px-3'}`}>
        {sidebarItems.map((item) => (
          <div key={item.id} className="mb-3">
            {isCollapsed ? (
              // Collapsed state with tooltip - centered within sidebar
              <div 
                className={`sidebar-item-collapsed sidebar-nav-item ${
                  isActiveRoute(item.route) ? 'active' : ''
                }`}
                onMouseEnter={() => setHoveredItem(item.id)}
                onMouseLeave={() => setHoveredItem(null)}
                onClick={() => handleItemClick(item)}
              >
                {item.icon}
                <div className={`sidebar-tooltip ${hoveredItem === item.id ? 'show' : ''}`}>
                  {item.label}
                </div>
              </div>
            ) : (
              // Expanded state
              <button
                onClick={() => handleItemClick(item)}
                className={`sidebar-nav-item w-full flex items-center justify-between px-3 py-2 rounded-lg text-left transition-colors ${
                  isActiveRoute(item.route) ? 'active' : ''
                }`}
              >
                <div className="flex items-center space-x-3">
                  {item.icon}
                  <span className="sidebar-label">{item.label}</span>
                </div>
                {item.subItems && (
                  <svg
                    className={`w-4 h-4 transition-transform ${
                      expandedItems.includes(item.id) ? 'rotate-90' : ''
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                )}
              </button>
            )}

            {/* Sub Items - only show when expanded and not collapsed */}
            {!isCollapsed && item.subItems && expandedItems.includes(item.id) && (
              <div className="ml-8 mt-1 space-y-1">
                {item.subItems.map((subItem) => (
                  <button
                    key={subItem.id}
                    onClick={() => handleSubItemClick(subItem)}
                    className={`sidebar-subitem w-full text-left px-3 py-2 rounded-lg sidebar-sublabel transition-colors ${
                      pathname === subItem.route ? 'active' : ''
                    }`}
                  >
                    {subItem.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </nav>

      {/* Profile Section */}
      {!isCollapsed && (
        <div className="absolute bottom-4 left-4 right-4">
          <button
            onClick={() => router.push('/settings/profile')}
            className={`sidebar-profile w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
              pathname === '/settings/profile' ? 'active' : ''
            }`}
          >
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-400 rounded-full flex items-center justify-center shadow-lg shadow-blue-400/30">
              <span className="text-white text-sm-app font-medium">U</span>
            </div>
            <div className="flex-1 text-left">
              <p className="sidebar-profile-name">User Profile</p>
              <p className="sidebar-profile-role">Settings & Account</p>
            </div>
          </button>
        </div>
      )}
      {isCollapsed && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <div 
            className="sidebar-item-collapsed sidebar-profile cursor-pointer"
            onMouseEnter={() => setHoveredItem('profile')}
            onMouseLeave={() => setHoveredItem(null)}
            onClick={() => router.push('/settings/profile')}
          >
            <svg className="sidebar-icon-collapsed" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <div className={`sidebar-tooltip ${hoveredItem === 'profile' ? 'show' : ''}`}>
              User Profile
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppSidebar;
