@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Typography - Professional Sans Font System */
@layer base {
  * {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  }
    html {
    scroll-behavior: smooth;
    font-size: 0.8rem; /* Reduced base font size for more compact design */
  }  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-weight: 400;
    line-height: 1.4; /* Tighter line height for compactness */
    color: #1e293b; /* slate-800 */
    background-color: #f8fafc; /* slate-50 */
    font-size: 1.1rem; /* Increased by 10% from 1rem (16px to 17.6px) */
  }
}

/* Professional Typography Scale - Comprehensive System */
@layer components {  /* Base typography classes with consistent scaling - Increased by 10% */
  .text-xs-app {
    font-size: 0.825rem; /* 13.2px - 10% increase from 12px */
    line-height: 1.1rem; /* 17.6px - 10% increase from 16px */
    font-weight: 400;
  }

  .text-sm-app {
    font-size: 0.9625rem; /* 15.4px - 10% increase from 14px */
    line-height: 1.375rem; /* 22px - 10% increase from 20px */
    font-weight: 400;
  }

  .text-base-app {
    font-size: 1.1rem; /* 17.6px - 10% increase from 16px */
    line-height: 1.65rem; /* 26.4px - 10% increase from 24px */
    font-weight: 400;
  }

  .text-lg-app {
    font-size: 1.2375rem; /* 19.8px - 10% increase from 18px */
    line-height: 1.925rem; /* 30.8px - 10% increase from 28px */
    font-weight: 500;
  }

  .text-xl-app {
    font-size: 1.375rem; /* 22px - 10% increase from 20px */
    line-height: 2.0625rem; /* 33px - 10% increase from 30px */
    font-weight: 600;
  }

  .text-2xl-app {
    font-size: 1.65rem; /* 26.4px - 10% increase from 24px */
    line-height: 2.2rem; /* 35.2px - 10% increase from 32px */
    font-weight: 700;
  }

  .text-3xl-app {
    font-size: 2.0625rem; /* 33px - 10% increase from 30px */
    line-height: 2.475rem; /* 39.6px - 10% increase from 36px */
    font-weight: 700;
  }

  .text-4xl-app {
    font-size: 2.475rem; /* 39.6px - 10% increase from 36px */
    line-height: 2.75rem; /* 44px - 10% increase from 40px */
    font-weight: 800;
  }

  .text-5xl-app {
    font-size: 3.3rem; /* 52.8px - 10% increase from 48px */
    line-height: 3.3rem; /* 52.8px - 10% increase from 48px */
    font-weight: 800;
  }
  /* Responsive typography classes - Increased by 10% */
  .text-responsive-sm {
    font-size: 0.9625rem; /* 15.4px - 10% increase from 14px */
    line-height: 1.375rem;
  }

  @media (min-width: 768px) {
    .text-responsive-sm {
      font-size: 1.1rem; /* 17.6px - 10% increase from 16px */
      line-height: 1.65rem;
    }
  }

  .text-responsive-base {
    font-size: 1.1rem; /* 17.6px - 10% increase from 16px */
    line-height: 1.65rem;
  }

  @media (min-width: 768px) {
    .text-responsive-base {
      font-size: 1.2375rem; /* 19.8px - 10% increase from 18px */
      line-height: 1.925rem;
    }
  }

  .text-responsive-lg {
    font-size: 1.2375rem; /* 19.8px - 10% increase from 18px */
    line-height: 1.925rem;
  }

  @media (min-width: 768px) {
    .text-responsive-lg {
      font-size: 1.375rem; /* 22px - 10% increase from 20px */
      line-height: 2.0625rem;
    }
  }

  .text-responsive-xl {
    font-size: 1.375rem; /* 22px - 10% increase from 20px */
    line-height: 2.0625rem;
  }

  @media (min-width: 768px) {
    .text-responsive-xl {
      font-size: 1.65rem; /* 26.4px - 10% increase from 24px */
      line-height: 2.2rem;
    }
  }

  .text-responsive-2xl {
    font-size: 1.65rem; /* 26.4px - 10% increase from 24px */
    line-height: 2.2rem;
  }

  @media (min-width: 768px) {
    .text-responsive-2xl {
      font-size: 2.0625rem; /* 33px - 10% increase from 30px */
      line-height: 2.475rem;
    }
  }

  .text-responsive-3xl {
    font-size: 2.0625rem; /* 33px - 10% increase from 30px */
    line-height: 2.475rem;
  }

  @media (min-width: 768px) {
    .text-responsive-3xl {
      font-size: 2.475rem; /* 39.6px - 10% increase from 36px */
      line-height: 2.75rem;
    }
  }

  .text-responsive-4xl {
    font-size: 2.475rem; /* 39.6px - 10% increase from 36px */
    line-height: 2.75rem;
  }

  @media (min-width: 768px) {
    .text-responsive-4xl {
      font-size: 3.3rem; /* 52.8px - 10% increase from 48px */
      line-height: 3.3rem;
    }
  }

  .text-responsive-5xl {
    font-size: 2.75rem; /* 44px - 10% increase from 40px */
    line-height: 3.025rem;
  }

  @media (min-width: 768px) {
    .text-responsive-5xl {
      font-size: 4.125rem; /* 66px - 10% increase from 60px */
      line-height: 4.125rem;
    }
  }
  
  /* Legacy Form Components - Updated to use standardized system */
  .form-input {
    @apply form-input-md;
  }

  .form-input:focus {
    @apply ring-2 ring-indigo-500/50 border-indigo-500/50 shadow-lg shadow-indigo-500/10;
  }

  .form-input.error {
    @apply border-red-300 focus:ring-red-500/50 focus:border-red-500/50 bg-red-50/50;
  }

  .form-input.success {
    @apply border-green-300 focus:ring-green-500/50 focus:border-green-500/50 bg-green-50/50;
  }

  .form-label {
    @apply text-sm-app font-medium text-slate-700 mb-1.5 block;
  }

  .form-label.required::after {
    content: " *";
    @apply text-red-500;
  }

  .form-error {
    @apply text-xs-app text-red-600 mt-1 flex items-center;
  }

  .form-success {
    @apply text-xs-app text-green-600 mt-1 flex items-center;
  }

  /* Responsive form inputs for mobile */
  @media (max-width: 640px) {
    .form-input {
      @apply form-input-sm;
    }
  }

  .form-button {
    @apply btn-md;
  }

  .form-button-primary {
    @apply btn-primary;
  }

  .form-button-secondary {
    @apply btn-secondary;
  }

  /* Legacy Card components - Updated to use standardized system */
  .card {
    @apply card-md;
  }

  .card-title {
    @apply card-title-md;
  }

  .card-content {
    @apply text-base-app text-slate-700;
  }

  .card-subtitle {
    @apply text-sm-app text-slate-600;
  }

  /* Legacy Table components - Updated to use standardized system */
  .table-header {
    @apply table-header-cell;
  }

  .table-cell {
    @apply text-base-app text-slate-700;
  }
  
  /* ===== STANDARDIZED DESIGN SYSTEM ===== */

  /* Button System - Consistent sizing and typography */
  .btn {
    @apply text-base-app font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    font-family: inherit;
    /* Standardized button sizes */
  }

  .btn-sm {
    @apply btn px-3 py-2 text-sm-app;
    height: 2.25rem; /* 36px */
  }

  .btn-md {
    @apply btn px-4 py-2.5 text-base-app;
    height: 2.5rem; /* 40px */
  }

  .btn-lg {
    @apply btn px-5 py-3 text-base-app;
    height: 2.75rem; /* 44px */
  }

  .btn-primary {
    @apply btn-md bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply btn-md bg-slate-200 text-slate-700 hover:bg-slate-300 focus:ring-slate-500 border border-slate-300;
  }

  .btn-danger {
    @apply btn-md bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow-md;
  }

  .btn-success {
    @apply btn-md bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-emerald-500 shadow-sm hover:shadow-md;
  }

  .btn-outline {
    @apply btn-md bg-white text-slate-700 hover:bg-slate-50 focus:ring-slate-500 border border-slate-300 shadow-sm hover:shadow-md;
  }

  /* Form System - Consistent input sizing and typography */
  .form-input-sm {
    @apply text-sm-app border border-slate-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent bg-white transition-all duration-200 placeholder-slate-400 hover:border-slate-400;
    height: 2.25rem; /* 36px */
    font-family: inherit;
  }

  .form-input-md {
    @apply text-base-app border border-slate-300 rounded-lg px-4 py-2.5 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent bg-white transition-all duration-200 placeholder-slate-400 hover:border-slate-400;
    height: 2.5rem; /* 40px */
    font-family: inherit;
  }

  .form-input-lg {
    @apply text-base-app border border-slate-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent bg-white transition-all duration-200 placeholder-slate-400 hover:border-slate-400;
    height: 2.75rem; /* 44px */
    font-family: inherit;
  }

  /* Default form input - medium size */
  .form-input {
    @apply form-input-md;
  }

  /* Select/Dropdown System */
  .form-select-sm {
    @apply form-input-sm appearance-none bg-white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  .form-select-md {
    @apply form-input-md appearance-none bg-white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  .form-select-lg {
    @apply form-input-lg appearance-none bg-white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  /* Default form select - medium size */
  .form-select {
    @apply form-select-md;
  }

  /* Table System - Consistent typography and spacing */
  .table-container {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden;
  }

  .table-header {
    @apply bg-gray-800 text-gray-200;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-sm-app font-medium uppercase tracking-wide;
    height: 3rem; /* 48px */
  }

  .table-row {
    @apply hover:bg-gray-50 transition-colors duration-150 border-b border-gray-200 last:border-b-0;
  }

  .table-cell {
    @apply px-6 py-3 text-base-app text-gray-900;
    height: 3.5rem; /* 56px */
  }

  .table-cell-secondary {
    @apply px-6 py-3 text-sm-app text-gray-600;
    height: 3.5rem; /* 56px */
  }

  /* Card System - Consistent padding and typography */
  .card-sm {
    @apply bg-white rounded-lg shadow-sm border border-slate-200 p-4;
  }

  .card-md {
    @apply bg-white rounded-lg shadow-sm border border-slate-200 p-6;
  }

  .card-lg {
    @apply bg-white rounded-xl shadow-lg border border-slate-200 p-8;
  }

  /* Default card - medium size */
  .card {
    @apply card-md;
  }

  .card-title-sm {
    @apply text-base-app font-semibold text-slate-900 mb-2;
  }

  .card-title-md {
    @apply text-lg-app font-semibold text-slate-900 mb-3;
  }

  .card-title-lg {
    @apply text-xl-app font-bold text-slate-900 mb-4;
  }

  /* Default card title - medium size */
  .card-title {
    @apply card-title-md;
  }

  .card-content {
    @apply text-base-app text-slate-700 leading-relaxed;
  }

  .card-subtitle {
    @apply text-sm-app text-slate-600 mb-2;
  }

  /* Section Header System - Consistent hierarchy */
  .section-header-sm {
    @apply text-lg-app font-semibold text-gray-900 mb-3;
  }

  .section-header-md {
    @apply text-xl-app font-bold text-gray-900 mb-4;
  }

  .section-header-lg {
    @apply text-2xl-app font-bold text-gray-900 mb-6;
  }

  .section-header-xl {
    @apply text-3xl-app font-bold text-gray-900 mb-8;
  }

  /* Page Header System */
  .page-header {
    @apply section-header-lg;
  }

  .page-subtitle {
    @apply text-base-app text-gray-600 mt-1;
  }

  /* Modal System - Consistent sizing and typography */
  .modal-overlay {
    @apply fixed inset-0 bg-gradient-to-br from-slate-900/80 via-slate-800/80 to-slate-900/80 flex items-center justify-center p-4 z-50 backdrop-blur-md;
  }

  .modal-container-sm {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-sm mx-auto relative shadow-2xl border border-white/20;
  }

  .modal-container-md {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-md mx-auto relative shadow-2xl border border-white/20;
  }

  .modal-container-lg {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-lg mx-auto relative shadow-2xl border border-white/20;
  }

  .modal-container-xl {
    @apply bg-white/95 backdrop-blur-sm rounded-xl w-full max-w-xl mx-auto relative shadow-2xl border border-white/20;
  }

  /* Default modal - medium size */
  .modal-container {
    @apply modal-container-md;
  }

  .modal-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .modal-title {
    @apply text-lg-app font-semibold text-gray-900;
  }

  .modal-content {
    @apply px-6 py-4 text-base-app text-gray-700;
  }

  .modal-footer {
    @apply px-6 py-4 border-t border-gray-200 flex items-center justify-end space-x-3;
  }
  
  /* Modal and overlay components */
  .modal-title {
    @apply text-xl-app font-semibold text-slate-900;
  }
  
  .modal-content {
    @apply text-base-app text-slate-700;
  }
}

/* Custom fade-in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Modern Auth Modal Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced dropdown slideIn animation */
@keyframes slideInDropdown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modalBackdrop {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(12px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse-border {
  0%, 100% {
    border-color: rgb(99 102 241 / 0.3);
    box-shadow: 0 0 0 0 rgb(99 102 241 / 0.7);
  }
  70% {
    border-color: rgb(99 102 241 / 0.6);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0);
  }
}

/* Animation classes */
.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-slideInLeft {
  animation: slideInFromLeft 0.8s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInFromRight 0.8s ease-out forwards;
}

.animate-float {
  animation: floatAnimation 3s ease-in-out infinite;
}

.animate-pulseGlow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-modalBackdrop {
  animation: modalBackdrop 0.3s ease-out;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.animate-pulse-border {
  animation: pulse-border 2s infinite;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom button styles */
.btn-primary {
  @apply bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg transition-all duration-200 hover:from-indigo-700 hover:to-purple-700 hover:shadow-xl transform hover:-translate-y-1;
}

.btn-secondary {
  @apply border-2 border-indigo-600 text-indigo-600 font-semibold py-3 px-6 rounded-lg transition-all duration-200 hover:bg-indigo-600 hover:text-white transform hover:-translate-y-1;
}

/* Loading state for form submissions */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced focus states for accessibility */
.focus-visible:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark theme scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Grid pattern for hero section */
.bg-grid-pattern {
  background-image: radial-gradient(circle, #e2e8f0 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced form field styles */
.form-field-enhanced {
  @apply relative;
}

.form-field-enhanced input:focus + .field-icon {
  @apply text-indigo-500 scale-110;
}

.form-field-enhanced:hover .field-icon {
  @apply text-slate-500;
}

/* Loading spinner enhancement */
@keyframes spin-enhanced {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner-enhanced {
  animation: spin-enhanced 1s linear infinite;
}

/* Enhanced sidebar and navigation styles */

/* Sidebar navigation hover effects */
.nav-item-enhanced {
  @apply relative overflow-hidden;
}

.nav-item-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left 0.6s ease;
}

.nav-item-enhanced:hover::before {
  left: 100%;
}

/* Active nav item glow effect */
.nav-item-active {
  box-shadow: 
    0 0 20px rgba(99, 102, 241, 0.3),
    0 0 40px rgba(99, 102, 241, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}  /* Enhanced Modern Dark Sidebar - Matching Header Background */
  .sidebar-dark {
    @apply bg-slate-900 border-slate-700;
    box-shadow: 4px 0 20px -8px rgba(0, 0, 0, 0.4);
  }
  .sidebar-dark .sidebar-header {
    @apply bg-slate-900 border-slate-700/50;
  }.sidebar-dark .sidebar-brand {
    @apply text-white font-bold;
    background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(96, 165, 250, 0.4);
  }  .sidebar-dark .sidebar-toggle-btn {
    @apply text-slate-300 hover:text-white transition-all duration-300;
    @apply hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-blue-400/20;
    @apply hover:shadow-lg hover:shadow-blue-400/20;
  }
  .sidebar-dark .sidebar-nav-item {
    @apply text-slate-300 transition-all duration-300 relative overflow-hidden;
    @apply hover:text-white hover:bg-gradient-to-r hover:from-slate-800/50 hover:to-slate-700/50;
    @apply hover:shadow-lg hover:shadow-blue-400/10;
    @apply mx-2 rounded-xl; /* Add consistent margin and border radius */
    backdrop-filter: blur(10px);
  }.sidebar-dark .sidebar-nav-item::before {
    content: '';
    @apply absolute left-0 top-0 bottom-0 w-1 transition-all duration-300;
    background: linear-gradient(180deg, transparent 0%, #60a5fa 50%, transparent 100%);
    opacity: 0;
    transform: scaleY(0);
  }

  .sidebar-dark .sidebar-nav-item:hover::before {
    opacity: 1;
    transform: scaleY(1);
  }  /* Fixed Issue #1: Active item positioning - maintain same horizontal position */
  .sidebar-dark .sidebar-nav-item.active {
    @apply text-white relative;
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.3) 0%, rgba(59, 130, 246, 0.4) 25%, rgba(37, 99, 235, 0.3) 50%, rgba(59, 130, 246, 0.4) 75%, rgba(96, 165, 250, 0.3) 100%);
    box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-left: 2px solid rgba(96, 165, 250, 0.6);
    backdrop-filter: blur(10px);
    /* Maintain same margin as inactive items to prevent shifting */
  }
  /* Arrow removed for cleaner look */

  .sidebar-dark .sidebar-nav-item .sidebar-label {
    @apply text-slate-200 font-medium;
  }

  .sidebar-dark .sidebar-nav-item.active .sidebar-label {
    @apply text-white font-semibold;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  }

  .sidebar-dark .sidebar-subitem {
    @apply text-slate-400 transition-all duration-300 relative;
    @apply hover:text-slate-200 hover:bg-gradient-to-r hover:from-slate-800/30 hover:to-slate-700/30;
    @apply ml-2 border-l border-slate-700/50;
  }

  .sidebar-dark .sidebar-subitem::before {
    content: '';
    @apply absolute -left-px top-1/2 -translate-y-1/2 w-3 h-px bg-slate-600;
  }  .sidebar-dark .sidebar-subitem.active {
    @apply text-blue-200 font-medium;
    background: linear-gradient(90deg, rgba(96, 165, 250, 0.12) 0%, rgba(59, 130, 246, 0.12) 100%);
    border-left-color: rgba(96, 165, 250, 0.7);
  }

  .sidebar-dark .sidebar-subitem.active::before {
    @apply bg-blue-300;
    opacity: 0.8;
  }  .sidebar-dark .sidebar-profile {
    @apply text-slate-300 transition-all duration-300;
    @apply hover:text-white hover:bg-gradient-to-r hover:from-slate-800/50 hover:to-slate-700/50;
    @apply hover:shadow-lg hover:shadow-blue-400/10;
    @apply mx-2 rounded-xl;
    backdrop-filter: blur(10px);
  }

  .sidebar-dark .sidebar-tooltip {
    @apply bg-slate-800 text-white shadow-xl;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  }

  .sidebar-dark .sidebar-tooltip::before {
    @apply border-r-slate-800;
  }  /* Modern Collapse Toggle Arrow - Made Even Bigger with Bright Blue Theme */
  .sidebar-collapse-toggle {
    @apply absolute -right-5 top-1/2 -translate-y-1/2 z-50;
    @apply bg-gradient-to-r from-blue-500 to-blue-400 rounded-full;
    @apply flex items-center justify-center cursor-pointer transition-all duration-300;
    @apply hover:from-blue-400 hover:to-blue-300 hover:scale-110;
    @apply shadow-lg shadow-blue-400/40;
    width: 2.5rem; /* 40px - increased from 32px */
    height: 2.5rem; /* 40px - increased from 32px */
    border: 2px solid rgba(15, 23, 42, 0.8);
  }

  .sidebar-collapse-toggle:hover {
    box-shadow: 0 0 25px rgba(96, 165, 250, 0.6);
  }

  .sidebar-collapse-toggle svg {
    @apply w-5 h-5 text-white transition-transform duration-300; /* Increased icon size */
  }

  .sidebar-collapse-toggle svg {
    @apply w-4 h-4 text-white transition-transform duration-300;
  }

  .sidebar-collapse-toggle.collapsed svg {
    @apply rotate-180;
  }

  /* Fixed Issue #3: Header background to match sidebar exactly */
  .header-dark {
    @apply bg-slate-900 border-slate-700 shadow-slate-900/10;
    /* Ensure exact match with sidebar background */
    background-color: rgb(15 23 42); /* Same as sidebar bg-slate-900 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  }
  .header-dark .header-title {
    @apply text-white;
    font-size: 1.2375rem; /* Match sidebar-brand (text-lg-app) size */
    font-weight: 700;
  }

  .header-dark .header-breadcrumb {
    @apply text-slate-400;
  }

  .header-dark .header-breadcrumb-current {
    @apply text-slate-200;
  }

  .header-dark .header-search-input {
    @apply bg-slate-800 border-slate-600 text-white placeholder-slate-400;
    @apply focus:bg-slate-700 focus:border-slate-500;
  }

  .header-dark .header-action-btn {
    @apply text-slate-300 hover:text-white hover:bg-slate-800;
  }

  .header-dark .header-profile-dropdown {
    @apply bg-slate-800 border-slate-600 shadow-slate-900/20;
  }

  .header-dark .header-profile-name {
    @apply text-white;
  }

  .header-dark .header-profile-role {
    @apply text-slate-400;
  }

  .header-dark .header-dropdown-item {
    @apply text-slate-300 hover:bg-slate-700 hover:text-white;
  }

  .header-dark .header-dropdown-item.danger {
    @apply text-red-400 hover:bg-red-600 hover:text-white;
  }

  /* Consistent header height */
  .header-height {
    @apply h-16; /* 64px height */
  }

  .sidebar-header-height {
    @apply h-16; /* 64px height to match header */
  }

  /* Enhanced glass morphism effect */
  .glass-morphism-enhanced {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Professional shadow utilities */
  .shadow-professional {
    box-shadow: 
      0 1px 3px rgba(0, 0, 0, 0.12),
      0 1px 2px rgba(0, 0, 0, 0.24);
  }

  .shadow-professional-lg {
    box-shadow: 
      0 10px 25px rgba(0, 0, 0, 0.1),
      0 6px 10px rgba(0, 0, 0, 0.12);
  }
  /* Enhanced animation keyframes */
  @keyframes tooltipSlideIn {
    0% {
      opacity: 0;
      transform: translateY(-50%) translateX(-8px);
    }
    100% {
      opacity: 1;
      transform: translateY(-50%) translateX(4px);
    }
  }

  @keyframes slideIn {
    0% {
      opacity: 0;
      transform: translateY(-50%) translateX(-8px);
    }
    100% {
      opacity: 1;
      transform: translateY(-50%) translateX(0);
    }
  }

  /* System status indicator animation */
  @keyframes pulse-gentle {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.05);
    }
  }

  .pulse-gentle {
    animation: pulse-gentle 2s infinite ease-in-out;
  }

  /* Enhanced gradient text */
  .gradient-text-professional {
    background: linear-gradient(135deg, #1e293b 0%, #475569 50%, #334155 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Custom sidebar font sizes - 5% smaller */
  .text-xs-small {
    font-size: 0.7125rem; /* 0.75rem * 0.95 = 0.7125rem */
    line-height: 1.1875rem; /* 1.25rem * 0.95 = 1.1875rem */
  }

  .text-sm-small {
    font-size: 0.83125rem; /* 0.875rem * 0.95 = 0.83125rem */
    line-height: 1.30625rem; /* 1.375rem * 0.95 = 1.30625rem */
  }

  /* Custom sidebar font sizes - 5% larger than original */
  .text-xs-large {
    font-size: 0.7875rem; /* 0.75rem * 1.05 = 0.7875rem */
    line-height: 1.3125rem; /* 1.25rem * 1.05 = 1.3125rem */
  }

  .text-sm-large {
    font-size: 0.91875rem; /* 0.875rem * 1.05 = 0.91875rem */
    line-height: 1.44375rem; /* 1.375rem * 1.05 = 1.44375rem */
  }

  /* Custom sidebar widths - 3% wider */
  .w-14-plus {
    width: 3.625rem; /* 56px * 1.03 = 57.68px ≈ 58px = 3.625rem */
  }

  .w-62 {
    width: 15.5rem; /* 240px * 1.03 = 247.2px ≈ 248px = 15.5rem */
  }

  /* Custom margins to match sidebar widths */
  .ml-14-plus {
    margin-left: 3.625rem; /* Match w-14-plus */
  }

  .ml-62 {
    margin-left: 15.5rem; /* Match w-62 */
  }

  /* Custom left positioning for separator */
  .left-14-plus {
    left: 3.625rem; /* Match w-14-plus */
  }

  .left-62 {
    left: 15.5rem; /* Match w-62 */
  }

  /* Smooth Animation for Tooltips */
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-10px) translateY(-50%);
    }
    to {
      opacity: 1;
      transform: translateX(0) translateY(-50%);
    }
  }

  /* Collapsed Sidebar Styles */
  @layer components {    /* Enhanced icons for collapsed sidebar with perfect centering */
    .sidebar-icon-collapsed {
      @apply w-6 h-6; /* Increased size for better visibility while staying within bounds */
      flex-shrink: 0; /* Prevent icon from shrinking */
      display: block;
      margin: 0 auto; /* Ensure perfect horizontal centering */
    }

    .sidebar-icon-expanded {
      @apply w-5 h-5; /* Keep current size for expanded state */
      flex-shrink: 0; /* Prevent icon from shrinking */
    }    /* Enhanced Tooltip styles for collapsed sidebar */
    .sidebar-tooltip {
      @apply absolute left-full ml-4 px-3 py-2 text-white text-sm-app rounded-lg shadow-xl;
      @apply opacity-0 invisible transform -translate-y-1/2 transition-all duration-300 ease-out;
      background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
      border: 1px solid rgba(96, 165, 250, 0.3);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5), 0 0 20px rgba(96, 165, 250, 0.3);
      white-space: nowrap;
      top: 50%;
      z-index: 9999;
      pointer-events: none;
      backdrop-filter: blur(10px);
    }

    .sidebar-tooltip::before {
      content: '';
      @apply absolute right-full top-1/2 -translate-y-1/2;
      border: 6px solid transparent;
      border-right: 6px solid #1e293b;
      filter: drop-shadow(-1px 0 1px rgba(168, 85, 247, 0.1));
    }

    .sidebar-tooltip.show {
      @apply opacity-100 visible;
      transform: translateY(-50%) translateX(4px);
      animation: tooltipSlideIn 0.3s ease-out forwards;
    }    /* Enhanced hover states for collapsed sidebar with perfect centering */
    .sidebar-item-collapsed {
      @apply relative flex items-center justify-center rounded-xl transition-all duration-300;
      @apply text-slate-300 hover:text-white;
      @apply mx-auto; /* Center horizontally within sidebar */
      width: 3rem; /* 48px - slightly larger to accommodate w-6 h-6 icons */
      height: 3rem; /* 48px - square shape */
      background: rgba(30, 41, 59, 0.5);
      border: 1px solid rgba(71, 85, 105, 0.3);
      backdrop-filter: blur(10px);
    }    .sidebar-item-collapsed:hover {
      background: linear-gradient(135deg, rgba(96, 165, 250, 0.2) 0%, rgba(59, 130, 246, 0.2) 100%);
      border-color: rgba(96, 165, 250, 0.5);
      transform: scale(1.05);
      box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
    }

    /* Fixed Issue #4: Collapsed sidebar active icon alignment - maintain center position */
    .sidebar-item-collapsed.active {
      @apply text-white;
      background: linear-gradient(135deg, rgba(96, 165, 250, 0.3) 0%, rgba(59, 130, 246, 0.3) 100%);
      border-color: rgba(96, 165, 250, 0.6);
      box-shadow: 0 4px 15px rgba(96, 165, 250, 0.4);
      /* Removed transform: scale(1.1) to prevent shifting */
    }

    .sidebar-item-collapsed:hover .sidebar-tooltip {
      @apply opacity-100 visible;
      transform: translateY(-50%) translateX(4px);
      animation: tooltipSlideIn 0.3s ease-out forwards;
    }

    /* Additional collapsed sidebar improvements */
  .sidebar-dark nav {
    overflow: hidden; /* Prevent any overflow issues */
  }

  /* Ensure proper spacing for collapsed navigation items */
  .sidebar-dark.w-16 nav {
    padding-left: 0.5rem; /* 8px padding for collapsed state */
    padding-right: 0.5rem; /* 8px padding for collapsed state */
  }

  /* Center all collapsed items perfectly within the 64px sidebar */
  .sidebar-dark .sidebar-item-collapsed {
    margin-left: auto;
    margin-right: auto;
    /* Ensure items don't exceed sidebar boundaries */
    max-width: calc(100% - 1rem); /* Leave 16px total margin */
  }

  /* Prevent profile section overflow in collapsed mode */
  .sidebar-dark .sidebar-profile.sidebar-item-collapsed {
    position: relative;
    left: 0;
    transform: none;
  }

    /* Enhanced sidebar typography */
    .sidebar-label {
      @apply text-base-app font-medium; /* Uses the increased font size */
    }

    .sidebar-sublabel {
      @apply text-sm-app; /* Uses the increased font size */
    }

    .sidebar-brand {
      @apply text-lg-app font-bold; /* Uses the increased font size */
    }

    .sidebar-profile-name {
      @apply text-sm-app font-medium; /* Uses the increased font size */
    }

    .sidebar-profile-role {
      @apply text-xs-app; /* Uses the increased font size */
    }
  }  /* Removed glassmorphism effects since sidebar now matches header solid background */

  /* Fixed Issue #1: Removed transform rules that cause active item shifting */
  /* .sidebar-dark .sidebar-nav-item:not(.sidebar-item-collapsed):hover {
    transform: translateX(2px);
  }

  .sidebar-dark .sidebar-nav-item:not(.sidebar-item-collapsed).active {
    transform: translateX(4px);
  } *//* Subtle glow effects */
  .sidebar-dark .sidebar-brand {
    filter: drop-shadow(0 0 12px rgba(96, 165, 250, 0.4));
  }

  /* Enhanced profile section */
  .sidebar-dark .sidebar-profile {
    @apply mx-2 rounded-xl;
    backdrop-filter: blur(10px);
  }
