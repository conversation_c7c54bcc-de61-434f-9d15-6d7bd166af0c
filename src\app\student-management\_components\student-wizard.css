/*
===============================================================================
                    STUDENT WIZARD COMPONENT STYLES
===============================================================================

This file contains styles specific to the Add Student Wizard component.
It inherits from the global design system and ensures consistent typography
and styling across all wizard steps.

DESIGN PRINCIPLES:
- Inherit typography from globals.css design system
- Consistent form element styling across all steps
- Professional appearance with proper spacing
- Responsive design for mobile and desktop

===============================================================================
*/

/* Import global design system */
@import '../../../globals.css';

/* Wizard Container Styles */
.student-wizard {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Wizard Header Styles */
.wizard-header {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
}

.wizard-title {
  @apply text-2xl-app font-bold text-white;
}

.wizard-step-indicator {
  @apply text-sm-app font-medium text-white;
}

.wizard-step-description {
  @apply text-xs-app text-white/80;
}

/* Form Element Styles - Consistent Typography */
.wizard-form-label {
  @apply text-sm-app font-semibold text-gray-800 mb-2 block;
  font-family: inherit;
}

.wizard-form-label.required::after {
  content: " *";
  @apply text-red-500;
}

.wizard-form-input {
  @apply w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-base-app font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md;
  font-family: inherit;
  height: 2.75rem; /* 44px - consistent height */
}

.wizard-form-input:focus {
  @apply ring-2 ring-indigo-500/20 border-indigo-500 shadow-lg;
}

.wizard-form-input.error {
  @apply border-red-300 focus:ring-red-500/20 focus:border-red-500 bg-red-50/50;
}

.wizard-form-select {
  @apply w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-base-app font-medium shadow-sm hover:shadow-md appearance-none;
  font-family: inherit;
  height: 2.75rem; /* 44px - consistent height */
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  padding-right: 2.5rem;
}

.wizard-form-select:focus {
  @apply ring-2 ring-indigo-500/20 border-indigo-500 shadow-lg;
}

.wizard-form-textarea {
  @apply w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 resize-none text-base-app font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md;
  font-family: inherit;
  min-height: 5rem; /* 80px - consistent minimum height */
}

.wizard-form-textarea:focus {
  @apply ring-2 ring-indigo-500/20 border-indigo-500 shadow-lg;
}

/* Date Input Specific Styles */
.wizard-form-date {
  @apply wizard-form-input text-sm-app;
}

/* Error Message Styles */
.wizard-form-error {
  @apply text-sm-app text-red-600 mt-1 flex items-center;
  font-family: inherit;
}

/* Button Styles */
.wizard-btn {
  @apply font-semibold rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  font-family: inherit;
}

.wizard-btn-primary {
  @apply wizard-btn px-8 py-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white hover:from-indigo-700 hover:to-blue-700 shadow-md hover:shadow-lg text-base-app;
}

.wizard-btn-secondary {
  @apply wizard-btn px-6 py-3 border border-gray-300 text-gray-700 hover:bg-gray-100 text-base-app;
}

.wizard-btn-success {
  @apply wizard-btn px-8 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white hover:from-green-700 hover:to-emerald-700 shadow-md hover:shadow-lg text-base-app;
}

.wizard-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Step Content Styles */
.wizard-step-content {
  @apply space-y-6;
}

.wizard-form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.wizard-form-group {
  @apply space-y-2;
}

/* File Upload Styles */
.wizard-file-upload {
  @apply border-2 border-dashed border-gray-200 rounded-xl p-6 text-center hover:border-indigo-400 hover:bg-indigo-50/30 transition-all duration-200 cursor-pointer bg-gray-50/30;
}

.wizard-file-upload:hover {
  @apply border-indigo-400 bg-indigo-50/30;
}

.wizard-file-upload-icon {
  @apply w-10 h-10 bg-indigo-100 rounded-xl flex items-center justify-center mx-auto mb-3;
}

.wizard-file-upload-text {
  @apply text-sm-app font-medium text-gray-700;
  font-family: inherit;
}

.wizard-file-upload-subtext {
  @apply text-xs-app text-gray-500 font-medium;
  font-family: inherit;
}

/* Step Progress Styles */
.wizard-step-progress {
  @apply flex items-center justify-between mt-6;
}

.wizard-step-circle {
  @apply w-10 h-10 rounded-full flex items-center justify-center font-semibold;
}

.wizard-step-circle.active {
  @apply bg-white text-indigo-600;
}

.wizard-step-circle.inactive {
  @apply bg-white/20 text-white/60;
}

.wizard-step-line {
  @apply ml-8 w-12 h-0.5;
}

.wizard-step-line.completed {
  @apply bg-white;
}

.wizard-step-line.incomplete {
  @apply bg-white/20;
}

/* Content Container */
.wizard-content {
  @apply p-6 overflow-y-auto max-h-[60vh];
}

/* Footer */
.wizard-footer {
  @apply border-t border-gray-200 p-6 bg-gray-50;
}

.wizard-footer-actions {
  @apply flex items-center justify-between;
}

/* Loading States */
.wizard-loading {
  @apply animate-spin rounded-full h-4 w-4 border-b-2 border-white;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .wizard-form-grid {
    @apply grid-cols-1 gap-4;
  }
  
  .wizard-form-input,
  .wizard-form-select,
  .wizard-form-textarea {
    @apply text-sm-app;
    height: 2.5rem; /* 40px - smaller on mobile */
  }
  
  .wizard-form-textarea {
    min-height: 4rem; /* 64px - smaller on mobile */
  }
  
  .wizard-btn-primary,
  .wizard-btn-success {
    @apply px-6 py-2.5 text-sm-app;
  }
  
  .wizard-btn-secondary {
    @apply px-4 py-2.5 text-sm-app;
  }
}

/* Focus and Accessibility */
.wizard-form-input:focus,
.wizard-form-select:focus,
.wizard-form-textarea:focus {
  @apply outline-none;
}

/* Validation States */
.wizard-form-input.valid {
  @apply border-green-300 focus:ring-green-500/20 focus:border-green-500 bg-green-50/50;
}

.wizard-form-select.valid {
  @apply border-green-300 focus:ring-green-500/20 focus:border-green-500 bg-green-50/50;
}

/* Animation Classes */
.wizard-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
