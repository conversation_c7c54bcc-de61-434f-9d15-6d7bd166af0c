// Quick test to check Supabase connection
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://njucxccmreiyhmobdzcf.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5qdWN4Y2NtcmVpeWhtb2JkemNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1MTY4NjksImV4cCI6MjA2NDA5Mjg2OX0.oKMhVhigaM3CR4EJuRN485-g1scjNrVrqFyIDc-_M0I';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  try {
    console.log('Testing Supabase connection...');
    
    // Test basic connection
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Supabase connection error:', error);
    } else {
      console.log('Supabase connection successful!');
      console.log('Session data:', data);
    }
    
  } catch (error) {
    console.error('Failed to connect to Supabase:', error);
  }
}

testConnection();
