 pro'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function NotFoundPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/dashboard?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const quickLinks = [
    {
      title: 'Dashboard',
      description: 'Overview of your school management system',
      href: '/dashboard',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
        </svg>
      ),
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-50 to-blue-100'
    },
    {
      title: 'Student Management',
      description: 'Manage student records, enrollment, and profiles',
      href: '/student-management',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      color: 'from-green-500 to-green-600',
      bgColor: 'from-green-50 to-green-100'
    },
    {
      title: 'Academic Management',
      description: 'Manage classes, subjects, and academic records',
      href: '/academic-management',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
      color: 'from-purple-500 to-purple-600',
      bgColor: 'from-purple-50 to-purple-100'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
      {/* Background Elements - matching your product page */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>
      </div>

      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Header with EduPro Branding */}
        <header className="bg-slate-900 text-white p-4 shadow-lg">
          <div className="container mx-auto flex items-center">
            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <svg
                width="36"
                height="36"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="text-indigo-400"
              >
                <path
                  d="M22 9L12 14L2 9L12 4L22 9ZM12 15.5L6 12.5V16.5L12 19.5L18 16.5V12.5L12 15.5Z"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M4 9.5V15L12 19.5L20 15V9.5L12 14L4 9.5Z"
                  fill="currentColor"
                  fillOpacity="0.3"
                />
              </svg>
              <h1 className="text-xl font-bold">EduPro</h1>
            </Link>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-grow container mx-auto px-4 py-16 flex items-center justify-center">
          <div className="max-w-6xl w-full">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              
              {/* Left Side - Visual + Message */}
              <div className="text-center lg:text-left space-y-8 animate-fadeInUp">
                {/* Creative 404 Visual */}
                <div className="relative">
                  <div className="text-8xl lg:text-9xl font-bold bg-gradient-to-r from-indigo-200 to-purple-200 bg-clip-text text-transparent select-none">
                    404
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full p-8 shadow-2xl border border-indigo-100">
                      <svg className="h-16 w-16 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Error Message */}
                <div className="space-y-4">
                  <h1 className="text-4xl lg:text-5xl font-bold text-slate-800">
                    <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                      Oops!
                    </span>
                    <br />
                    Page Not Found
                  </h1>
                  <p className="text-lg text-slate-600 leading-relaxed max-w-lg">
                    This page seems to have taken a study break! Don't worry – EduPro has many other 
                    <span className="font-semibold text-indigo-600"> powerful features</span> to help you manage your educational institution.
                  </p>
                </div>

                {/* Trust Indicators */}
                <div className="flex flex-col sm:flex-row gap-4 text-sm text-slate-500">
                  <div className="flex items-center space-x-2">
                    <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>10,000+ Schools Trust EduPro</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span>4.9/5 Customer Rating</span>
                  </div>
                </div>
              </div>

              {/* Right Side - Action Panel */}
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/50 p-8 space-y-8 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
                <div>
                  <h2 className="text-2xl font-bold text-slate-800 mb-2">What would you like to do?</h2>
                  <p className="text-slate-600">Let's get you back to managing your educational institution.</p>
                </div>

                {/* Search Box */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Search EduPro Features
                  </label>
                  <form onSubmit={handleSearch} className="relative">
                    <input
                      type="text"
                      placeholder="Try 'student enrollment' or 'attendance tracking'"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full h-14 pl-12 pr-24 rounded-2xl border-2 border-indigo-100 focus:border-indigo-500 focus:ring-4 focus:ring-indigo-50 transition-all duration-200 bg-white/80 backdrop-blur-sm"
                    />
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                    <button
                      type="submit"
                      className="absolute inset-y-0 right-2 my-2 px-6 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105"
                    >
                      Search
                    </button>
                  </form>
                </div>

                {/* Quick Actions */}
                <div className="space-y-4">
                  <h3 className="font-semibold text-slate-800">Quick Navigation</h3>
                  <div className="grid gap-3">
                    {quickLinks.map((link) => (
                      <Link
                        key={link.href}
                        href={link.href}
                        className="group flex items-center p-4 rounded-2xl bg-gradient-to-r from-white/60 to-white/80 hover:from-white/80 hover:to-white/90 border border-white/50 hover:border-indigo-200 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg"
                      >
                        <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${link.bgColor} flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>
                          <div className={`text-transparent bg-gradient-to-r ${link.color} bg-clip-text`}>
                            {link.icon}
                          </div>
                        </div>
                        <div className="ml-4 flex-1">
                          <h4 className="font-semibold text-slate-800 group-hover:text-indigo-600 transition-colors">
                            {link.title}
                          </h4>
                          <p className="text-sm text-slate-600 mt-1">
                            {link.description}
                          </p>
                        </div>
                        <svg className="w-5 h-5 text-slate-400 group-hover:text-indigo-600 group-hover:translate-x-1 transition-all duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    ))}
                  </div>
                </div>

                {/* Primary Actions */}
                <div className="flex flex-col sm:flex-row gap-3">
                  <Link 
                    href="/dashboard"
                    className="flex-1 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-4 rounded-2xl font-semibold text-center hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-xl"
                  >
                    Go to Dashboard
                  </Link>
                  <Link 
                    href="/"
                    className="flex-1 border-2 border-indigo-200 text-indigo-600 px-6 py-4 rounded-2xl font-semibold text-center hover:border-indigo-500 hover:bg-indigo-50 transition-all duration-200 transform hover:-translate-y-1"
                  >
                    Return Home
                  </Link>
                </div>
              </div>
            </div>

            {/* Bottom Help Section */}
            <div className="mt-20 text-center animate-fadeInUp" style={{animationDelay: '0.4s'}}>
              <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-3xl p-8 border border-indigo-100 shadow-lg max-w-4xl mx-auto">
                <div className="mb-6">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full mb-4">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-slate-800 mb-2">Need Help?</h3>
                  <p className="text-slate-600 max-w-2xl mx-auto">
                    Our education technology experts are here to help you get the most out of EduPro. 
                    We're committed to your school's success.
                  </p>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button
                    onClick={() => {
                      const subject = encodeURIComponent('EduPro - Page Not Found Report');
                      const body = encodeURIComponent(`I was looking for a page that doesn't exist.\n\nURL: ${window.location.href}\nTimestamp: ${new Date().toISOString()}\n\nAdditional details:`);
                      window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
                    }}
                    className="px-8 py-3 bg-white border-2 border-indigo-200 text-indigo-600 rounded-2xl font-semibold hover:border-indigo-500 hover:bg-indigo-50 transition-all duration-200 transform hover:-translate-y-1"
                  >
                    Contact Support
                  </button>
                  <Link 
                    href="/help"
                    className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-2xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg"
                  >
                    Visit Help Center
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/dashboard?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const quickLinks = [
    {
      title: 'Dashboard',
      description: 'Overview of your school management system',
      href: '/dashboard',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
        </svg>
      ),
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-50 to-blue-100'
    },
    {
      title: 'Student Management',
      description: 'Manage student records, enrollment, and profiles',
      href: '/student-management',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      color: 'from-green-500 to-green-600',
      bgColor: 'from-green-50 to-green-100'
    },
    {
      title: 'Academic Management',
      description: 'Manage classes, subjects, and academic records',
      href: '/academic-management',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
      color: 'from-purple-500 to-purple-600',
      bgColor: 'from-purple-50 to-purple-100'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
      {/* Background Elements - matching your product page */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>
      </div>

      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Header with EduPro Branding */}
        <header className="bg-slate-900 text-white p-4 shadow-lg">
          <div className="container mx-auto flex items-center">
            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <svg
                width="36"
                height="36"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="text-indigo-400"
              >
                <path
                  d="M22 9L12 14L2 9L12 4L22 9ZM12 15.5L6 12.5V16.5L12 19.5L18 16.5V12.5L12 15.5Z"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M4 9.5V15L12 19.5L20 15V9.5L12 14L4 9.5Z"
                  fill="currentColor"
                  fillOpacity="0.3"
                />
              </svg>
              <h1 className="text-xl font-bold">EduPro</h1>
            </Link>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-grow container mx-auto px-4 py-16 flex items-center justify-center">
          <div className="max-w-6xl w-full">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              
              {/* Left Side - Visual + Message */}
              <div className="text-center lg:text-left space-y-8 animate-fadeInUp">
                {/* Creative 404 Visual */}
                <div className="relative">
                  <div className="text-8xl lg:text-9xl font-bold bg-gradient-to-r from-indigo-200 to-purple-200 bg-clip-text text-transparent select-none">
                    404
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full p-8 shadow-2xl border border-indigo-100">
                      <svg className="h-16 w-16 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Error Message */}
                <div className="space-y-4">
                  <h1 className="text-4xl lg:text-5xl font-bold text-slate-800">
                    <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                      Oops!
                    </span>
                    <br />
                    Page Not Found
                  </h1>
                  <p className="text-lg text-slate-600 leading-relaxed max-w-lg">
                    This page seems to have taken a study break! Don't worry – EduPro has many other 
                    <span className="font-semibold text-indigo-600"> powerful features</span> to help you manage your educational institution.
                  </p>
                </div>

                {/* Trust Indicators */}
                <div className="flex flex-col sm:flex-row gap-4 text-sm text-slate-500">
                  <div className="flex items-center space-x-2">
                    <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>10,000+ Schools Trust EduPro</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span>4.9/5 Customer Rating</span>
                  </div>
                </div>
              </div>

              {/* Right Side - Action Panel */}
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/50 p-8 space-y-8 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
                <div>
                  <h2 className="text-2xl font-bold text-slate-800 mb-2">What would you like to do?</h2>
                  <p className="text-slate-600">Let's get you back to managing your educational institution.</p>
                </div>

                {/* Search Box */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Search EduPro Features
                  </label>
                  <form onSubmit={handleSearch} className="relative">
                    <input
                      type="text"
                      placeholder="Try 'student enrollment' or 'attendance tracking'"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full h-14 pl-12 pr-24 rounded-2xl border-2 border-indigo-100 focus:border-indigo-500 focus:ring-4 focus:ring-indigo-50 transition-all duration-200 bg-white/80 backdrop-blur-sm"
                    />
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                    <button
                      type="submit"
                      className="absolute inset-y-0 right-2 my-2 px-6 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105"
                    >
                      Search
                    </button>
                  </form>
                </div>

                {/* Quick Actions */}
                <div className="space-y-4">
                  <h3 className="font-semibold text-slate-800">Quick Navigation</h3>
                  <div className="grid gap-3">
                    {quickLinks.map((link) => (
                      <Link
                        key={link.href}
                        href={link.href}
                        className="group flex items-center p-4 rounded-2xl bg-gradient-to-r from-white/60 to-white/80 hover:from-white/80 hover:to-white/90 border border-white/50 hover:border-indigo-200 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg"
                      >
                        <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${link.bgColor} flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>
                          <div className={`text-transparent bg-gradient-to-r ${link.color} bg-clip-text`}>
                            {link.icon}
                          </div>
                        </div>
                        <div className="ml-4 flex-1">
                          <h4 className="font-semibold text-slate-800 group-hover:text-indigo-600 transition-colors">
                            {link.title}
                          </h4>
                          <p className="text-sm text-slate-600 mt-1">
                            {link.description}
                          </p>
                        </div>
                        <svg className="w-5 h-5 text-slate-400 group-hover:text-indigo-600 group-hover:translate-x-1 transition-all duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    ))}
                  </div>
                </div>

                {/* Primary Actions */}
                <div className="flex flex-col sm:flex-row gap-3">
                  <Link 
                    href="/dashboard"
                    className="flex-1 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-4 rounded-2xl font-semibold text-center hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-xl"
                  >
                    Go to Dashboard
                  </Link>
                  <Link 
                    href="/"
                    className="flex-1 border-2 border-indigo-200 text-indigo-600 px-6 py-4 rounded-2xl font-semibold text-center hover:border-indigo-500 hover:bg-indigo-50 transition-all duration-200 transform hover:-translate-y-1"
                  >
                    Return Home
                  </Link>
                </div>
              </div>
            </div>

            {/* Bottom Help Section */}
            <div className="mt-20 text-center animate-fadeInUp" style={{animationDelay: '0.4s'}}>
              <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-3xl p-8 border border-indigo-100 shadow-lg max-w-4xl mx-auto">
                <div className="mb-6">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full mb-4">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-slate-800 mb-2">Need Help?</h3>
                  <p className="text-slate-600 max-w-2xl mx-auto">
                    Our education technology experts are here to help you get the most out of EduPro. 
                    We're committed to your school's success.
                  </p>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button
                    onClick={() => {
                      const subject = encodeURIComponent('EduPro - Page Not Found Report');
                      const body = encodeURIComponent(`I was looking for a page that doesn't exist.\n\nURL: ${window.location.href}\nTimestamp: ${new Date().toISOString()}\n\nAdditional details:`);
                      window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
                    }}
                    className="px-8 py-3 bg-white border-2 border-indigo-200 text-indigo-600 rounded-2xl font-semibold hover:border-indigo-500 hover:bg-indigo-50 transition-all duration-200 transform hover:-translate-y-1"
                  >
                    Contact Support
                  </button>
                  <Link 
                    href="/help"
                    className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-2xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg"
                  >
                    Visit Help Center
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
    },
    {
      title: 'Student Management',
      description: 'Manage student records, enrollment, and profiles',
      href: '/student-management',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      color: 'bg-green-100 text-green-600'
    },
    {
      title: 'Staff Management',
      description: 'Manage teaching and administrative staff',
      href: '/staff-management',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 616 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      color: 'bg-purple-100 text-purple-600'
    },
    {
      title: 'Academic Management',
      description: 'Manage classes, subjects, and academic records',
      href: '/academic-management',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
      color: 'bg-orange-100 text-orange-600'
    },
    {
      title: 'Attendance Management',
      description: 'Track and manage student and staff attendance',
      href: '/attendance-management',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      ),
      color: 'bg-teal-100 text-teal-600'
    },
    {
      title: 'Fee Management',
      description: 'Manage student fees, payments, and financial records',
      href: '/fee-management',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: 'bg-indigo-100 text-indigo-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-4xl w-full">
        {/* Main 404 Content */}
        <div className="text-center mb-12">
          {/* 404 Illustration */}
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-32 h-32 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full mb-6">
              <span className="text-4xl-app font-bold text-indigo-600">404</span>
            </div>
            
            {/* EduPro Brand */}
            <div className="flex items-center justify-center mb-6">
              <div className="w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center mr-3">
                <span className="text-white font-bold text-base-app">E</span>
              </div>
              <span className="text-xl-app font-semibold text-gray-900">EduPro</span>
            </div>
          </div>

          {/* Error Message */}
          <h1 className="section-header-lg mb-4">Page Not Found</h1>
          <p className="card-content text-gray-600 mb-8 max-w-2xl mx-auto">
            The page you're looking for doesn't exist or may have been moved. 
            Don't worry - EduPro has many other features to help you manage your educational institution effectively.
          </p>

          {/* Search Bar */}
          <div className="max-w-md mx-auto mb-12">
            <form onSubmit={handleSearch} className="relative">
              <input
                type="text"
                placeholder="Search for pages, features, or help..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="form-input-lg w-full pl-12 pr-4"
              />
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <button
                type="submit"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <span className="btn-primary btn-sm">Search</span>
              </button>
            </form>
          </div>
        </div>

        {/* Quick Navigation Links */}
        <div className="mb-12">
          <h2 className="section-header-sm text-center mb-8">Explore EduPro Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quickLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="card-md hover:shadow-lg transition-all duration-200 group"
              >
                <div className="flex items-start space-x-4">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${link.color} group-hover:scale-110 transition-transform duration-200`}>
                    {link.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="card-title-sm group-hover:text-indigo-600 transition-colors">
                      {link.title}
                    </h3>
                    <p className="text-sm-app text-gray-600 mt-1">
                      {link.description}
                    </p>
                  </div>
                  <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Additional Actions */}
        <div className="text-center">
          <div className="card-lg bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200">
            <h3 className="card-title-sm mb-4">Need Help?</h3>
            <p className="card-content text-gray-600 mb-6">
              If you believe this page should exist or you're experiencing technical difficulties, 
              our support team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link href="/dashboard" className="btn-primary">
                Go to Dashboard
              </Link>
              <Link href="/" className="btn-secondary">
                Return Home
              </Link>
              <button
                onClick={() => {
                  const subject = encodeURIComponent('EduPro - Page Not Found Report');
                  const body = encodeURIComponent(`I was looking for a page that doesn't exist.\n\nURL: ${window.location.href}\nTimestamp: ${new Date().toISOString()}\n\nAdditional details:`);
                  window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
                }}
                className="btn-outline"
              >
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
