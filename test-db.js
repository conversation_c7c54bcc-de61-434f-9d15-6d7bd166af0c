// Test database queries
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://njucxccmreiyhmobdzcf.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5qdWN4Y2NtcmVpeWhtb2JkemNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1MTY4NjksImV4cCI6MjA2NDA5Mjg2OX0.oKMhVhigaM3CR4EJuRN485-g1scjNrVrqFyIDc-_M0I';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testDatabaseQueries() {
  try {
    console.log('Testing database queries...');
    
    // Test profiles table query
    console.log('Testing profiles table...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (profilesError) {
      console.error('Profiles table error:', profilesError);
    } else {
      console.log('Profiles table accessible:', profiles);
    }
    
    // Test auth getUser
    console.log('Testing auth getUser...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('Auth getUser error:', userError);
    } else {
      console.log('Auth getUser successful:', user ? 'User found' : 'No user');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testDatabaseQueries();
